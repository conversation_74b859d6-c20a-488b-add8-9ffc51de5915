// Global Variables
let currentJobTitle = '';

// Application Modal Functions
function openApplication(jobTitle) {
    trackJobApplication(jobTitle); // Add tracking here
    currentJobTitle = jobTitle;
    document.getElementById('modalJobTitle').textContent = jobTitle;
    document.getElementById('applicationModal').style.display = 'block';
}

function closeApplication() {
    document.getElementById('applicationModal').style.display = 'none';
    document.getElementById('cvAnalysisResult').style.display = 'none';
}

// CV Analysis Functions
function analyzeCv() {
    const file = document.getElementById('cvFile').files[0];
    if (file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (!allowedTypes.includes(file.type)) {
            alert('❌ Please upload a valid CV file (PDF, DOC, or DOCX)');
            return;
        }
        
        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            alert('❌ File size should be less than 5MB');
            return;
        }
        
        // Show loading state
        const uploadArea = document.querySelector('.upload-area');
        const originalContent = uploadArea.innerHTML;
        uploadArea.innerHTML = '<div>⏳</div><p>Analyzing your CV...</p><small>Please wait</small>';
        
        // Simulate CV analysis
        setTimeout(() => {
            uploadArea.innerHTML = '<div>✅</div><p>CV Analysis Complete!</p><small>Match scores updated</small>';
            
            setTimeout(() => {
                alert('🎉 CV uploaded successfully! Your profile has been enhanced with AI-powered analysis. You can now see personalized match scores for each job.');
                uploadArea.innerHTML = originalContent;
                addMatchScoresToJobs();
            }, 1500);
        }, 2000);
    }
}

function analyzeApplicationCv() {
    const file = document.getElementById('applicationCv').files[0];
    if (file) {
        // Validate file
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (!allowedTypes.includes(file.type)) {
            alert('❌ Please upload a valid CV file (PDF, DOC, or DOCX)');
            return;
        }
        
        document.getElementById('cvAnalysisResult').style.display = 'block';
        
        // Show loading state
        document.getElementById('skillsMatch').textContent = '...';
        document.getElementById('experienceMatch').textContent = '...';
        document.getElementById('overallMatch').textContent = '...';
        document.getElementById('analysisInsights').innerHTML = '<p>🔍 Analyzing your CV against job requirements...</p>';
        
        // Simulate AI analysis with realistic scores based on job type
        setTimeout(() => {
            const jobScores = getJobSpecificScores(currentJobTitle);
            const skillsScore = jobScores.skills;
            const experienceScore = jobScores.experience;
            const overallScore = Math.floor((skillsScore + experienceScore) / 2);
            
            // Animate score display
            animateScore('skillsMatch', skillsScore);
            animateScore('experienceMatch', experienceScore);
            animateScore('overallMatch', overallScore);
            
            // Update progress bars
            document.getElementById('skillsProgress').style.width = skillsScore + '%';
            document.getElementById('experienceProgress').style.width = experienceScore + '%';
            document.getElementById('overallProgress').style.width = overallScore + '%';
            
            // Generate insights
            const insights = getAnalysisInsights(skillsScore, experienceScore, overallScore);
            document.getElementById('analysisInsights').innerHTML = insights;
        }, 3000);
    }
}

function getJobSpecificScores(jobTitle) {
    // Generate more realistic scores based on job type
    const baseScores = {
        'Senior AI/ML Engineer': { skills: [80, 95], experience: [85, 95] },
        'Machine Learning Engineer': { skills: [75, 90], experience: [70, 85] },
        'IoT Solutions Architect': { skills: [70, 85], experience: [80, 90] },
        'Embedded Systems Developer': { skills: [65, 80], experience: [70, 85] },
        'Full Stack Developer': { skills: [75, 88], experience: [75, 88] },
        'Frontend Developer': { skills: [70, 85], experience: [65, 80] },
        'Digital Marketing Manager': { skills: [78, 90], experience: [80, 92] },
        'Growth Marketing Specialist': { skills: [72, 85], experience: [70, 82] },
        'Data Scientist': { skills: [82, 95], experience: [78, 90] },
        'Junior Data Analyst': { skills: [60, 75], experience: [55, 70] },
        'AI Research Intern': { skills: [65, 80], experience: [50, 65] }
    };
    
    const scores = baseScores[jobTitle] || { skills: [70, 85], experience: [70, 85] };
    
    return {
        skills: Math.floor(Math.random() * (scores.skills[1] - scores.skills[0] + 1)) + scores.skills[0],
        experience: Math.floor(Math.random() * (scores.experience[1] - scores.experience[0] + 1)) + scores.experience[0]
    };
}

function animateScore(elementId, targetScore) {
    const element = document.getElementById(elementId);
    let currentScore = 0;
    const increment = targetScore / 30; // 30 frames for smooth animation
    
    const timer = setInterval(() => {
        currentScore += increment;
        if (currentScore >= targetScore) {
            currentScore = targetScore;
            clearInterval(timer);
        }
        element.textContent = Math.floor(currentScore) + '%';
    }, 50);
}

function getAnalysisInsights(skills, experience, overall) {
    let insights = '<h5>🎯 Key Insights:</h5><ul>';
    
    // Skills insights
    if (skills >= 90) {
        insights += '<li>🌟 <strong>Exceptional skills match!</strong> Your technical expertise perfectly aligns with our requirements.</li>';
    } else if (skills >= 80) {
        insights += '<li>✅ <strong>Excellent skills match!</strong> Your technical skills align perfectly with the requirements.</li>';
    } else if (skills >= 70) {
        insights += '<li>👍 <strong>Good skills match.</strong> Most required skills are present in your profile.</li>';
    } else if (skills >= 60) {
        insights += '<li>💡 <strong>Moderate skills match.</strong> Consider highlighting more relevant technical skills.</li>';
    } else {
        insights += '<li>📚 <strong>Skills development opportunity.</strong> Focus on building key technical skills for this role.</li>';
    }
    
    // Experience insights
    if (experience >= 90) {
        insights += '<li>🎯 <strong>Perfect experience match!</strong> Your background is exactly what we\'re looking for.</li>';
    } else if (experience >= 80) {
        insights += '<li>✅ <strong>Experience level is ideal</strong> for this position.</li>';
    } else if (experience >= 65) {
        insights += '<li>👍 <strong>Relevant experience found.</strong> Your background matches the role requirements.</li>';
    } else if (experience >= 50) {
        insights += '<li>💡 <strong>Consider emphasizing</strong> project experience and achievements that relate to this role.</li>';
    } else {
        insights += '<li>🚀 <strong>Growth potential identified.</strong> Highlight transferable skills and learning experiences.</li>';
    }
    
    // Overall insights
    if (overall >= 90) {
        insights += '<li>🏆 <strong>Outstanding candidate!</strong> You\'re in the top 5% for this position.</li>';
    } else if (overall >= 85) {
        insights += '<li>🎉 <strong>Exceptional candidate!</strong> You\'re in the top tier for this position.</li>';
    } else if (overall >= 75) {
        insights += '<li>🚀 <strong>Strong candidate.</strong> You have a great chance of moving forward.</li>';
    } else if (overall >= 65) {
        insights += '<li>💪 <strong>Potential candidate.</strong> Highlighting key achievements could boost your application.</li>';
    } else {
        insights += '<li>📈 <strong>Development opportunity.</strong> Consider building experience in key areas for this role.</li>';
    }
    
    // Add specific recommendations
    if (overall >= 80) {
        insights += '<li>🎯 <strong>Next step:</strong> Prepare for our technical assessment and showcase your project experience.</li>';
    } else if (overall >= 70) {
        insights += '<li>📝 <strong>Tip:</strong> Emphasize specific projects and quantifiable achievements in your application.</li>';
    } else {
        insights += '<li>💡 <strong>Suggestion:</strong> Consider applying for related roles that match your current skill level.</li>';
    }
    
    insights += '</ul>';
    
    // Add job-specific insights
    insights += getJobSpecificInsights(currentJobTitle, skills, experience);
    
    return insights;
}

function getJobSpecificInsights(jobTitle, skills, experience) {
    const jobInsights = {
        'Senior AI/ML Engineer': '<p><strong>💡 Role-specific insight:</strong> This position requires deep expertise in machine learning frameworks and production deployment experience.</p>',
        'Machine Learning Engineer': '<p><strong>💡 Role-specific insight:</strong> Focus on MLOps experience and your ability to deploy models in production environments.</p>',
        'IoT Solutions Architect': '<p><strong>💡 Role-specific insight:</strong> Emphasize your experience with IoT protocols, edge computing, and system integration.</p>',
        'Frontend Developer': '<p><strong>💡 Role-specific insight:</strong> Showcase your UI/UX portfolio and modern JavaScript framework experience.</p>',
        'Digital Marketing Manager': '<p><strong>💡 Role-specific insight:</strong> Highlight your campaign performance metrics and growth achievements.</p>'
    };
    
    return jobInsights[jobTitle] || '<p><strong>💡 General tip:</strong> Tailor your application to highlight the most relevant experience for this specific role.</p>';
}

function addMatchScoresToJobs() {
    const jobCards = document.querySelectorAll('.job-card');
    jobCards.forEach((card, index) => {
        if (!card.querySelector('.match-score')) {
            // Generate realistic match scores based on job category and level
            const category = card.getAttribute('data-category');
            const level = card.getAttribute('data-level');
            
            let baseScore = 70;
            
            // Adjust base score by category (simulate user's stronger areas)
            const categoryBonus = {
                'ai': 15,
                'data': 10,
                'web': 8,
                'iot': 5,
                'marketing': 3
            };
            
            // Adjust by experience level
            const levelAdjustment = {
                'entry': 10,
                'mid': 5,
                'senior': -5
            };
            
            baseScore += (categoryBonus[category] || 0);
            baseScore += (levelAdjustment[level] || 0);
            
            // Add some randomness
            const score = Math.min(95, Math.max(60, baseScore + Math.floor(Math.random() * 20) - 10));
            
            const matchDiv = document.createElement('div');
            matchDiv.className = 'match-score';
            matchDiv.innerHTML = `${score}% Match`;
            card.style.position = 'relative';
            card.appendChild(matchDiv);
            
            // Add a subtle entrance animation
            setTimeout(() => {
                matchDiv.style.opacity = '0';
                matchDiv.style.transform = 'scale(0.8)';
                matchDiv.style.transition = 'all 0.3s ease';
                
                setTimeout(() => {
                    matchDiv.style.opacity = '1';
                    matchDiv.style.transform = 'scale(1)';
                }, 100);
            }, index * 200);
        }
    });
}

// Form Submission
function submitApplication(event) {
    event.preventDefault();
    
    // Validate required fields
    const requiredFields = ['fullName', 'email', 'phone', 'applicationCv'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.style.borderColor = '#e74c3c';
            isValid = false;
        } else {
            field.style.borderColor = '#e9ecef';
        }
    });
    
    if (!isValid) {
        alert('❌ Please fill in all required fields');
        return;
    }
    
    // Validate email format
    const email = document.getElementById('email').value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('❌ Please enter a valid email address');
        return;
    }
    
    // Submit application
    const submitBtn = event.target.querySelector('.submit-btn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = 'Submitting... ⏳';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
        const formData = new FormData(event.target);
        const applicationData = {
            jobTitle: currentJobTitle,
            fullName: formData.get('fullName'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            experience: formData.get('experience'),
            currentSalary: formData.get('currentSalary'),
            coverLetter: formData.get('coverLetter'),
            timestamp: new Date().toISOString()
        };
        
        // Store application data (in a real app, this would go to a server)
        console.log('Application submitted:', applicationData);
        
        const successMessage = `🎉 Application submitted successfully for ${currentJobTitle}!

Thank you for your interest in Cube AI Solution. Our AI-powered screening system will review your application and you'll hear from us within 48 hours.

Next steps:
• AI Resume Analysis (Completed ✅)
• Technical Assessment (If shortlisted)
• AI Interview Round  
• Final Interview with Team Lead

We'll keep you updated via email at ${applicationData.email}!

Application ID: CA-${Date.now().toString().slice(-6)}`;
        
        alert(successMessage);
        closeApplication();
        
        // Reset form
        event.target.reset();
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
}

// Search and Filter Functions
function filterJobs() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilters = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
        .map(cb => cb.value);
    
    const jobCards = document.querySelectorAll('.job-card');
    let visibleCount = 0;
    
    jobCards.forEach(card => {
        const title = card.querySelector('.job-title').textContent.toLowerCase();
        const description = card.querySelector('.job-description').textContent.toLowerCase();
        const skills = card.querySelector('.job-skills').textContent.toLowerCase();
        const category = card.getAttribute('data-category');
        const level = card.getAttribute('data-level');
        
        const matchesSearch = searchTerm === '' || 
            title.includes(searchTerm) || 
            description.includes(searchTerm) || 
            skills.includes(searchTerm);
        
        const matchesFilter = categoryFilters.length === 0 || 
            categoryFilters.includes(category) || 
            categoryFilters.includes(level) ||
            categoryFilters.includes('fulltime') || // Default work type
            categoryFilters.includes('remote');
        
        if (matchesSearch && matchesFilter) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });
    
    document.getElementById('jobCount').textContent = visibleCount;
    
    // Show message if no jobs found
    if (visibleCount === 0) {
        if (!document.getElementById('noJobsMessage')) {
            const message = document.createElement('div');
            message.id = 'noJobsMessage';
            message.style.cssText = `
                text-align: center;
                padding: 3rem;
                color: #666;
                font-size: 1.1rem;
            `;
            message.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
                <h3>No jobs found</h3>
                <p>Try adjusting your search criteria or filters</p>
            `;
            document.getElementById('jobList').appendChild(message);
        }
    } else {
        const existingMessage = document.getElementById('noJobsMessage');
        if (existingMessage) {
            existingMessage.remove();
        }
    }
}

// Save Job Functionality
function saveJob(jobTitle, button) {
    const savedJobs = JSON.parse(localStorage.getItem('savedJobs') || '[]');
    
    if (savedJobs.includes(jobTitle)) {
        // Unsave job
        const index = savedJobs.indexOf(jobTitle);
        savedJobs.splice(index, 1);
        button.textContent = 'Save Job';
        button.style.background = 'transparent';
        button.style.color = '#667eea';
    } else {
        // Save job
        savedJobs.push(jobTitle);
        button.textContent = 'Saved ✓';
        button.style.background = '#667eea';
        button.style.color = 'white';
    }
    
    localStorage.setItem('savedJobs', JSON.stringify(savedJobs));
}

// Initialize saved jobs state
function initializeSavedJobs() {
    const savedJobs = JSON.parse(localStorage.getItem('savedJobs') || '[]');
    const saveButtons = document.querySelectorAll('.save-btn');
    
    saveButtons.forEach(button => {
        const jobCard = button.closest('.job-card');
        const jobTitle = jobCard.querySelector('.job-title').textContent;
        
        if (savedJobs.includes(jobTitle)) {
            button.textContent = 'Saved ✓';
            button.style.background = '#667eea';
            button.style.color = 'white';
        }
        
        button.onclick = () => saveJob(jobTitle, button);
    });
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Cube AI Solution Recruitment Portal Loaded');
    
    // Initialize
    filterJobs();
    initializeSavedJobs();
    
    // Add search listener
    document.getElementById('searchInput').addEventListener('input', filterJobs);
    
    // Add filter listeners
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', filterJobs);
    });
    
    // Close modal when clicking outside
    window.onclick = function(event) {
        const modal = document.getElementById('applicationModal');
        if (event.target === modal) {
            closeApplication();
        }
    };
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(event) {
        // Escape key to close modal
        if (event.key === 'Escape') {
            closeApplication();
        }
        
        // Ctrl/Cmd + K to focus search
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            document.getElementById('searchInput').focus();
        }
    });
    
    // Add smooth scrolling for better UX
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to search
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const debouncedFilter = debounce(filterJobs, 300);
    
    searchInput.removeEventListener('input', filterJobs);
    searchInput.addEventListener('input', debouncedFilter);
});

// Analytics and tracking (placeholder)
function trackEvent(eventName, eventData) {
    console.log(`📊 Event: ${eventName}`, eventData);
    // In a real application, this would send data to analytics service
}

// Track job applications
function trackJobApplication(jobTitle) {
    trackEvent('job_application_started', {
        jobTitle: jobTitle,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
    });
}