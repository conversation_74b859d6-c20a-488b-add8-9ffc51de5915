# 📁 CAS Recruiter Candidate UI - Project Structure

## 🏗️ Directory Organization

```
CAS RECRUITER CANDIDATE UI/
│
├── 📁 assets/                    # Static Assets
│   ├── 📁 images/               # Image files (logos, backgrounds, etc.)
│   └── 📁 icons/                # Icon files (favicons, UI icons)
│
├── 📁 css/                      # Stylesheets
│   └── 📄 styles.css           # Main application stylesheet
│
├── 📁 js/                       # JavaScript Files
│   └── 📄 scripts.js           # Main application logic & functionality
│
├── 📁 pages/                    # HTML Pages
│   ├── 📄 login.html           # Authentication page (Sign In/Sign Up)
│   └── 📄 dashboard.html       # Main dashboard (Job listings & applications)
│
├── 📁 config/                   # Configuration Files
│   └── 📄 app.config.js        # Application settings & constants
│
├── 📁 node_modules/             # Dependencies (auto-generated)
│   └── ...                     # NPM packages
│
├── 📄 index.html               # Entry Point (Landing/Redirect page)
├── 📄 package.json             # Project configuration & dependencies
├── 📄 package-lock.json        # Dependency lock file
├── 📄 README.md                # Project documentation
└── 📄 PROJECT_STRUCTURE.md     # This file
```

## 🔗 File Relationships & Dependencies

### **Entry Flow:**
```
index.html → pages/login.html → pages/dashboard.html
```

### **Asset Dependencies:**
```
pages/login.html     (standalone - no external CSS/JS)
pages/dashboard.html → css/styles.css + js/scripts.js
index.html          (standalone - inline styles/scripts)
```

### **Configuration:**
```
config/app.config.js → Global settings for all pages
```

## 📋 File Descriptions

### **Root Files:**
- **`index.html`** - Smart entry point that checks authentication and redirects
- **`package.json`** - NPM configuration, scripts, and dependencies
- **`README.md`** - Project documentation and setup instructions

### **Pages Directory:**
- **`login.html`** - Complete authentication system with user database
- **`dashboard.html`** - Job listings, CV upload, applications, and user management

### **Assets Directory:**
- **`css/styles.css`** - All styling for dashboard and components
- **`js/scripts.js`** - Job filtering, CV analysis, application logic
- **`assets/images/`** - Future location for company logos, backgrounds
- **`assets/icons/`** - Future location for UI icons and favicons

### **Configuration:**
- **`config/app.config.js`** - Centralized settings for API, UI, authentication

## 🚀 Development Workflow

### **Starting the Application:**
```bash
npm start  # Opens index.html → Auto-redirects based on auth status
```

### **File Modification Guidelines:**
1. **Styling Changes** → Edit `css/styles.css`
2. **Dashboard Logic** → Edit `js/scripts.js`
3. **Authentication** → Edit `pages/login.html`
4. **Job Listings** → Edit `pages/dashboard.html`
5. **App Settings** → Edit `config/app.config.js`

### **Adding New Features:**
1. **New Pages** → Add to `pages/` directory
2. **New Styles** → Add to `css/` directory
3. **New Scripts** → Add to `js/` directory
4. **New Assets** → Add to `assets/images/` or `assets/icons/`

## 🔧 Technical Architecture

### **Frontend Stack:**
- **HTML5** - Semantic markup
- **CSS3** - Modern styling with gradients, animations
- **Vanilla JavaScript** - No frameworks, pure JS
- **LocalStorage** - Client-side data persistence

### **Authentication System:**
- **User Database** - LocalStorage-based user management
- **Session Management** - Persistent login state
- **Route Protection** - Automatic redirects based on auth status

### **Build System:**
- **Live Server** - Development server with hot reload
- **No Build Process** - Direct file serving for simplicity

## 📊 Data Flow

```
User Registration → LocalStorage (cubeai_users_db)
User Login → LocalStorage (cubeai_user_logged_in, cubeai_user_email, etc.)
Job Applications → JavaScript processing → Local storage
CV Analysis → Client-side processing → UI updates
```

## 🔐 Security Considerations

- **Client-side only** - No server-side validation
- **LocalStorage** - Data persists in browser
- **No encryption** - Passwords stored in plain text (development only)
- **Session management** - Basic token-based authentication

## 🎯 Future Enhancements

1. **Backend Integration** - Replace localStorage with real API
2. **Password Encryption** - Hash passwords before storage
3. **File Upload** - Real CV processing and storage
4. **Email Integration** - Actual email notifications
5. **Admin Panel** - Recruiter dashboard for managing applications

---

*This structure provides a clean, maintainable, and scalable foundation for the CAS Recruiter Candidate UI application.*
