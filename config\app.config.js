// Cube AI Solution - Application Configuration
const AppConfig = {
    // Application Information
    app: {
        name: "Cube AI Solution - Recruitment Portal",
        version: "1.0.0",
        description: "AI-Powered Talent Acquisition Platform",
        author: "Cube AI Solution",
        website: "https://cubeaisolution.com"
    },

    // API Configuration (for future backend integration)
    api: {
        baseUrl: "https://api.cubeaisolution.com",
        version: "v1",
        timeout: 30000,
        endpoints: {
            auth: "/auth",
            users: "/users",
            jobs: "/jobs",
            applications: "/applications",
            analytics: "/analytics"
        }
    },

    // Authentication Settings
    auth: {
        tokenKey: "cubeai_auth_token",
        userKey: "cubeai_user_data",
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        rememberMeDuration: 30 * 24 * 60 * 60 * 1000, // 30 days
        passwordMinLength: 6,
        emailRegex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },

    // Local Storage Keys
    storage: {
        users: "cubeai_users_db",
        currentUser: "cubeai_current_user",
        loginState: "cubeai_user_logged_in",
        userEmail: "cubeai_user_email",
        userName: "cubeai_user_name",
        userId: "cubeai_user_id",
        preferences: "cubeai_user_preferences"
    },

    // UI Configuration
    ui: {
        theme: {
            primary: "#667eea",
            secondary: "#764ba2",
            success: "#28a745",
            warning: "#ffc107",
            error: "#dc3545",
            info: "#17a2b8"
        },
        animations: {
            fadeIn: 300,
            slideIn: 400,
            loading: 1500
        },
        pagination: {
            itemsPerPage: 10,
            maxVisiblePages: 5
        }
    },

    // Job Categories and Filters
    jobs: {
        categories: [
            { id: "ai", name: "Artificial Intelligence", icon: "🤖" },
            { id: "iot", name: "IoT & Embedded Systems", icon: "🔗" },
            { id: "web", name: "Web Development", icon: "💻" },
            { id: "marketing", name: "Digital Marketing", icon: "📈" },
            { id: "data", name: "Data Science", icon: "📊" }
        ],
        experienceLevels: [
            { id: "entry", name: "Entry Level (0-2 years)", min: 0, max: 2 },
            { id: "mid", name: "Mid Level (3-5 years)", min: 3, max: 5 },
            { id: "senior", name: "Senior Level (5+ years)", min: 5, max: 99 }
        ],
        workTypes: [
            { id: "fulltime", name: "Full-time" },
            { id: "parttime", name: "Part-time" },
            { id: "remote", name: "Remote" },
            { id: "hybrid", name: "Hybrid" }
        ]
    },

    // CV Analysis Configuration
    cvAnalysis: {
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ],
        scoringWeights: {
            skills: 0.4,
            experience: 0.3,
            education: 0.2,
            projects: 0.1
        }
    },

    // Development Settings
    development: {
        debug: true,
        mockData: true,
        apiDelay: 1000,
        logLevel: "info"
    },

    // Production Settings
    production: {
        debug: false,
        mockData: false,
        apiDelay: 0,
        logLevel: "error"
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppConfig;
} else if (typeof window !== 'undefined') {
    window.AppConfig = AppConfig;
}
