<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube AI Solution - Recruitment Portal</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎯</text></svg>">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-container {
            text-align: center;
            color: white;
            max-width: 400px;
            padding: 2rem;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            color: white;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
            50% { transform: scale(1.05); box-shadow: 0 12px 40px rgba(0,0,0,0.4); }
            100% { transform: scale(1); box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 300;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            font-weight: 300;
        }
        
        .description {
            font-size: 1rem;
            opacity: 0.8;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status {
            font-size: 0.9rem;
            opacity: 0.7;
            font-style: italic;
        }
        
        .features {
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .feature-icon {
            margin-right: 0.8rem;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">C³</div>
        <h1>Cube AI Solution</h1>
        <p class="subtitle">Recruitment Portal</p>
        <p class="description">AI-Powered Talent Acquisition Platform</p>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">🤖</span>
                <span>AI-Powered CV Analysis</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🎯</span>
                <span>Smart Job Matching</span>
            </div>
            <div class="feature">
                <span class="feature-icon">📊</span>
                <span>Real-time Analytics</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔐</span>
                <span>Secure Authentication</span>
            </div>
        </div>
        
        <div class="spinner"></div>
        <p class="status">Initializing system...</p>
    </div>

    <script>
        // Check if user is already logged in
        const isLoggedIn = localStorage.getItem('cubeai_user_logged_in');
        const userEmail = localStorage.getItem('cubeai_user_email');
        
        // Update status message
        const statusElement = document.querySelector('.status');
        
        setTimeout(() => {
            if (isLoggedIn === 'true' && userEmail) {
                statusElement.textContent = 'User authenticated. Redirecting to dashboard...';
                // User is logged in, redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'pages/dashboard.html';
                }, 1500);
            } else {
                statusElement.textContent = 'Redirecting to login...';
                // User not logged in, redirect to login
                setTimeout(() => {
                    window.location.href = 'pages/login.html';
                }, 1500);
            }
        }, 1000);
    </script>
</body>
</html>
