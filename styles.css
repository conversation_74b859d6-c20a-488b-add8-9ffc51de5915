/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 30px rgba(0,0,0,0.1);
}

/* Header Styles */
header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
}

.company-info h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.company-info p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.header-nav {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-btn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 0.7rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* Main Content Layout */
.main-content {
    display: flex;
    min-height: calc(100vh - 100px);
}

/* Sidebar Styles */
.sidebar {
    width: 350px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    padding: 2rem;
}

.search-section {
    margin-bottom: 2rem;
}

.search-section h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1.2rem;
}

.search-input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
}

.filter-section {
    margin-bottom: 2rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group h4 {
    margin-bottom: 0.8rem;
    color: #34495e;
    font-size: 1rem;
}

.filter-option {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    cursor: pointer;
}

.filter-option input {
    margin-right: 0.8rem;
    accent-color: #667eea;
}

.cv-upload-section {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
}

.cv-upload-section h3 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.upload-area {
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    padding: 2rem;
    margin: 1rem 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: rgba(255,255,255,0.6);
    background: rgba(255,255,255,0.1);
}

.upload-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.upload-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Job Listings Styles */
.job-listings {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.listings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.listings-header h2 {
    color: #2c3e50;
    font-size: 1.8rem;
}

.job-count {
    color: #666;
    font-size: 1rem;
}

.job-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.job-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.job-department {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.job-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.9rem;
}

.job-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.job-description {
    color: #555;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.job-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.skill-tag {
    background: #f8f9fa;
    color: #495057;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid #e9ecef;
}

.job-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.apply-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.save-btn {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn:hover {
    background: #667eea;
    color: white;
}

.job-posted {
    color: #888;
    font-size: 0.8rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Form Styles */
.application-form {
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

/* CV Analysis Styles */
.cv-analysis {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.analysis-score {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.score-item {
    text-align: center;
    flex: 1;
}

.score-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.score-label {
    font-size: 0.9rem;
    color: #666;
}

.progress-bar {
    background: #e9ecef;
    height: 8px;
    border-radius: 4px;
    margin: 0.5rem 0;
}

.progress-fill {
    background: linear-gradient(135deg, #667eea, #764ba2);
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    width: 100%;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Match Score Styles */
.match-score {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .job-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .job-actions {
        flex-direction: column;
    }
    
    .analysis-score {
        flex-direction: column;
        gap: 1rem;
    }
    
    .modal-content {
        margin: 2% auto;
        width: 95%;
        max-height: 90vh;
    }
}

@media (max-width: 480px) {
    .container {
        margin: 0;
        box-shadow: none;
    }
    
    .header-content {
        padding: 0 1rem;
    }
    
    .sidebar,
    .job-listings {
        padding: 1rem;
    }
    
    .job-card {
        padding: 1rem;
    }
    
    .modal-content {
        padding: 1rem;
        margin: 1% auto;
        width: 98%;
    }
}