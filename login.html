<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube AI Solution - Candidate Portal</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👤</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            line-height: 1.5;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid #e5e5e5;
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.5rem;
            height: 65px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 42px;
            height: 42px;
            background: linear-gradient(135deg, #1a73e8, #4285f4);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            font-weight: bold;
            color: white;
        }

        .company-info h1 {
            font-size: 1.4rem;
            font-weight: 500;
            color: #202124;
            margin: 0;
        }

        .company-info p {
            font-size: 0.8rem;
            color: #5f6368;
            margin: 0;
        }

        .header-nav {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }

        .nav-link {
            color: #5f6368;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 400;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: #1a73e8;
        }

        .nav-link.active {
            color: #1a73e8;
            font-weight: 500;
        }

        /* Main Container */
        .main-container {
            display: flex;
            min-height: calc(100vh - 65px);
        }

        /* Left Side - Info Panel */
        .info-panel {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            position: relative;
            overflow: hidden;
        }

        .info-panel::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: drift 20s linear infinite;
        }

        @keyframes drift {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        .info-content {
            text-align: center;
            color: white;
            position: relative;
            z-index: 2;
            max-width: 400px;
        }

        .info-content h2 {
            font-size: 2.2rem;
            font-weight: 300;
            margin-bottom: 1.5rem;
            line-height: 1.3;
        }

        .info-content p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .features-list {
            list-style: none;
            text-align: left;
            margin-top: 2rem;
        }

        .features-list li {
            padding: 0.8rem 0;
            font-size: 1rem;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .features-list li::before {
            content: '✓';
            background: rgba(255, 255, 255, 0.2);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* Right Side - Login Panel */
        .login-panel {
            flex: 1;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem 2rem;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 400;
            color: #202124;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            font-size: 1rem;
            color: #5f6368;
            font-weight: 400;
        }

        /* Tab Switcher */
        .tab-switcher {
            display: flex;
            background: #f1f3f4;
            border-radius: 6px;
            padding: 4px;
            margin-bottom: 2rem;
        }

        .tab-btn {
            flex: 1;
            padding: 0.8rem 1rem;
            background: transparent;
            border: none;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: 500;
            color: #5f6368;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tab-btn.active {
            background: white;
            color: #1a73e8;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Form Styles */
        .auth-form {
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #3c4043;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .form-input.error {
            border-color: #d93025;
        }

        .error-message {
            color: #d93025;
            font-size: 0.8rem;
            margin-top: 0.5rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        /* Form Options */
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #1a73e8;
        }

        .checkbox-group label {
            color: #3c4043;
            font-weight: 400;
            cursor: pointer;
            margin: 0;
        }

        .forgot-link {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        /* Buttons */
        .btn-primary {
            width: 100%;
            padding: 0.875rem 1.5rem;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            background: #1557b0;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-primary.loading {
            pointer-events: none;
        }

        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
        }

        .btn-primary.loading .btn-text {
            opacity: 0;
        }

        .btn-primary.loading .loading-spinner {
            opacity: 1;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Alternative Actions */
        .alt-actions {
            text-align: center;
            padding-top: 1.5rem;
            border-top: 1px solid #e8eaed;
        }

        .alt-actions p {
            color: #5f6368;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .btn-secondary {
            width: 100%;
            padding: 0.875rem 1.5rem;
            background: white;
            color: #3c4043;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .btn-secondary:hover {
            background: #f8f9fa;
            border-color: #c4c7c5;
        }

        .signup-link {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link:hover {
            text-decoration: underline;
        }

        /* Footer */
        .footer {
            background: white;
            border-top: 1px solid #e5e5e5;
            padding: 1.5rem 0;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
        }

        .footer-links a {
            color: #5f6368;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .footer-links a:hover {
            color: #1a73e8;
            text-decoration: underline;
        }

        .footer-text {
            color: #5f6368;
            font-size: 0.8rem;
        }

        /* Success Message */
        .success-message {
            background: #e8f5e8;
            color: #137333;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #137333;
            display: none;
        }

        .success-message.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .info-panel {
                min-height: 300px;
                padding: 2rem 1rem;
            }

            .info-content h2 {
                font-size: 1.8rem;
            }

            .features-list {
                margin-top: 1rem;
            }

            .login-panel {
                padding: 2rem 1rem;
            }

            .header-content {
                padding: 0 1rem;
            }

            .company-info h1 {
                font-size: 1.2rem;
            }

            .footer-links {
                flex-wrap: wrap;
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .header-content {
                flex-direction: column;
                height: auto;
                padding: 1rem;
                gap: 1rem;
            }

            .header-nav {
                gap: 1rem;
            }

            .info-content h2 {
                font-size: 1.5rem;
            }

            .login-container {
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="company-logo">C³</div>
                <div class="company-info">
                    <h1>Cube AI Solution</h1>
                    <p>Candidate Portal</p>
                </div>
            </div>
            <nav class="header-nav">
                <a href="https://www.cubeaisolutions.com/About%20us/about%20us.html" class="nav-link">About Us</a>
                <a href="https://www.cubeaisolutions.com/Service%20Page/Service.html" class="nav-link">Careers</a>
                <a href="#" class="nav-link active">Candidate Login</a>
                <a href="https://www.cubeaisolutions.com/contact-us/contact.html" class="nav-link">Contact</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Right Panel - Login Form -->
        <section class="login-panel" style="flex: 1 1 100%;">
            <div class="login-container">
                <div class="login-header">
                    <h1 class="login-title">Welcome Back</h1>
                    <p class="login-subtitle">Sign in to access your candidate dashboard</p>
                </div>

                <!-- Tab Switcher -->
                <div class="tab-switcher">
                    <button class="tab-btn active" onclick="switchTab('signin')">Sign In</button>
                    <button class="tab-btn" onclick="switchTab('signup')">Sign Up</button>
                </div>

                <!-- Success Message -->
                <div id="successMessage" class="success-message">
                    <strong>Account created successfully!</strong> Please sign in with your credentials.
                </div>

                <!-- Sign In Form -->
                <form id="signinForm" class="auth-form">
                    <div class="form-group">
                        <label for="signinEmail">Email Address</label>
                        <input type="email" id="signinEmail" class="form-input" placeholder="Enter your email address" required>
                        <div class="error-message" id="signinEmailError">Please enter a valid email address</div>
                    </div>

                    <div class="form-group">
                        <label for="signinPassword">Password</label>
                        <input type="password" id="signinPassword" class="form-input" placeholder="Enter your password" required>
                        <div class="error-message" id="signinPasswordError">Password is required</div>
                    </div>

                    <div class="form-options">
                        <div class="checkbox-group">
                            <input type="checkbox" id="rememberMe">
                            <label for="rememberMe">Remember me</label>
                        </div>
                        <a href="#" class="forgot-link" onclick="showForgotPassword()">Forgot password?</a>
                    </div>

                    <button type="submit" class="btn-primary">
                        <span class="btn-text">Sign In</span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>

                <!-- Sign Up Form -->
                <form id="signupForm" class="auth-form" style="display: none;">
                    <div class="form-group">
                        <label for="signupName">Full Name</label>
                        <input type="text" id="signupName" class="form-input" placeholder="Enter your full name" required>
                        <div class="error-message" id="signupNameError">Full name is required</div>
                    </div>

                    <div class="form-group">
                        <label for="signupEmail">Email Address</label>
                        <input type="email" id="signupEmail" class="form-input" placeholder="Enter your email address" required>
                        <div class="error-message" id="signupEmailError">Please enter a valid email address</div>
                    </div>

                    <div class="form-group">
                        <label for="signupPhone">Phone Number</label>
                        <input type="tel" id="signupPhone" class="form-input" placeholder="Enter your phone number" required>
                        <div class="error-message" id="signupPhoneError">Phone number is required</div>
                    </div>

                    <div class="form-group">
                        <label for="signupPassword">Password</label>
                        <input type="password" id="signupPassword" class="form-input" placeholder="Create a password" required>
                        <div class="error-message" id="signupPasswordError">Password must be at least 6 characters</div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" class="form-input" placeholder="Confirm your password" required>
                        <div class="error-message" id="confirmPasswordError">Passwords do not match</div>
                    </div>

                    <div class="form-options">
                        <div class="checkbox-group">
                            <input type="checkbox" id="agreeTerms" required>
                            <label for="agreeTerms">I agree to the <a href="#" class="signup-link">Terms of Service</a></label>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary">
                        <span class="btn-text">Create Account</span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>

                <!-- Alternative Actions -->
                <div class="alt-actions">
                    <p>Or continue with</p>
                    <button class="btn-secondary" onclick="signInWithGoogle()">
                        <span style="margin-right: 8px;">🔍</span> Sign in with Google
                    </button>
                    <button class="btn-secondary" onclick="signInWithLinkedIn()">
                        <span style="margin-right: 8px;">💼</span> Sign in with LinkedIn
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Help Center</a>
                <a href="#">Contact Support</a>
            </div>
            <p class="footer-text">© 2025 Cube AI Solution. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Global variables
        let currentTab = 'signin';

        // Tab switching functionality
        function switchTab(tab) {
            currentTab = tab;
            const signinForm = document.getElementById('signinForm');
            const signupForm = document.getElementById('signupForm');
            const tabBtns = document.querySelectorAll('.tab-btn');
            const successMessage = document.getElementById('successMessage');
            
            // Hide success message when switching tabs
            successMessage.classList.remove('show');
            
            // Update tab buttons
            tabBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide forms
            if (tab === 'signin') {
                signinForm.style.display = 'block';
                signupForm.style.display = 'none';
                document.querySelector('.login-title').textContent = 'Welcome Back';
                document.querySelector('.login-subtitle').textContent = 'Sign in to access your candidate dashboard';
            } else {
                signinForm.style.display = 'none';
                signupForm.style.display = 'block';
                document.querySelector('.login-title').textContent = 'Create Account';
                document.querySelector('.login-subtitle').textContent = 'Join our talent community today';
            }
            
            // Clear any error states
            clearFormErrors();
        }

        // Form validation functions
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function validatePhone(phone) {
            const phoneRegex = /^[\+]?[1-9][\d]{9,14}$/;
            return phoneRegex.test(phone.replace(/\s/g, ''));
        }

        function showFieldError(fieldId, errorId, message) {
            const field = document.getElementById(fieldId);
            const error = document.getElementById(errorId);
            
            field.classList.add('error');
            error.textContent = message;
            error.classList.add('show');
        }

        function clearFieldError(fieldId, errorId) {
            const field = document.getElementById(fieldId);
            const error = document.getElementById(errorId);
            
            field.classList.remove('error');
            error.classList.remove('show');
        }

        function clearFormErrors() {
            const errorMessages = document.querySelectorAll('.error-message');
            const errorFields = document.querySelectorAll('.form-input.error');
            
            errorMessages.forEach(error => error.classList.remove('show'));
            errorFields.forEach(field => field.classList.remove('error'));
        }

        // Sign In Form Handler
        document.getElementById('signinForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('signinEmail').value.trim();
            const password = document.getElementById('signinPassword').value;
            let isValid = true;
            
            // Clear previous errors
            clearFormErrors();
            
            // Validate email
            if (!email) {
                showFieldError('signinEmail', 'signinEmailError', 'Email address is required');
                isValid = false;
            } else if (!validateEmail(email)) {
                showFieldError('signinEmail', 'signinEmailError', 'Please enter a valid email address');
                isValid = false;
            }
            
            // Validate password
            if (!password) {
                showFieldError('signinPassword', 'signinPasswordError', 'Password is required');
                isValid = false;
            }
            
            if (isValid) {
                // Show loading state
                const submitBtn = this.querySelector('.btn-primary');
                submitBtn.classList.add('loading');
                
                // Simulate authentication
                setTimeout(() => {
                    // Check demo credentials
                    if (email === '<EMAIL>' && password === 'password123') {
                        // Store login state in localStorage
                        localStorage.setItem('cubeai_user_logged_in', 'true');
                        localStorage.setItem('cubeai_user_email', email);
                        localStorage.setItem('cubeai_user_name', 'Demo Candidate');

                        // Redirect to dashboard.html after successful login
                        window.location.href = "dashboard.html";
                    } else {
                        alert('❌ Invalid credentials. Please try:\n\nEmail: <EMAIL>\nPassword: password123');
                    }

                    submitBtn.classList.remove('loading');
                }, 2000);
            }
        });

        // Sign Up Form Handler
        document.getElementById('signupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('signupName').value.trim();
            const email = document.getElementById('signupEmail').value.trim();
            const phone = document.getElementById('signupPhone').value.trim();
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;
            let isValid = true;
            
            // Clear previous errors
            clearFormErrors();
            
            // Validate name
            if (!name) {
                showFieldError('signupName', 'signupNameError', 'Full name is required');
                isValid = false;
            }
            
            // Validate email
            if (!email) {
                showFieldError('signupEmail', 'signupEmailError', 'Email address is required');
                isValid = false;
            } else if (!validateEmail(email)) {
                showFieldError('signupEmail', 'signupEmailError', 'Please enter a valid email address');
                isValid = false;
            }
            
            // Validate phone
            if (!phone) {
                showFieldError('signupPhone', 'signupPhoneError', 'Phone number is required');
                isValid = false;
            } else if (!validatePhone(phone)) {
                showFieldError('signupPhone', 'signupPhoneError', 'Please enter a valid phone number');
                isValid = false;
            }
            
            // Validate password
            if (!password) {
                showFieldError('signupPassword', 'signupPasswordError', 'Password is required');
                isValid = false;
            } else if (password.length < 6) {
                showFieldError('signupPassword', 'signupPasswordError', 'Password must be at least 6 characters');
                isValid = false;
            }
            
            // Validate confirm password
            if (!confirmPassword) {
                showFieldError('confirmPassword', 'confirmPasswordError', 'Please confirm your password');
                isValid = false;
            } else if (password !== confirmPassword) {
                showFieldError('confirmPassword', 'confirmPasswordError', 'Passwords do not match');
                isValid = false;
            }
            
            // Validate terms agreement
            if (!agreeTerms) {
                alert('❌ Please agree to the Terms of Service to continue');
                isValid = false;
            }
            
            if (isValid) {
                // Show loading state
                const submitBtn = this.querySelector('.btn-primary');
                submitBtn.classList.add('loading');
                
                // Simulate account creation
                setTimeout(() => {
                    submitBtn.classList.remove('loading');

                    // Store login state in localStorage (auto-login after signup)
                    localStorage.setItem('cubeai_user_logged_in', 'true');
                    localStorage.setItem('cubeai_user_email', email);
                    localStorage.setItem('cubeai_user_name', name);

                    // Show success message briefly then redirect
                    document.getElementById('successMessage').classList.add('show');

                    setTimeout(() => {
                        // Redirect to dashboard after successful signup
                        window.location.href = "dashboard.html";
                    }, 2000);
                }, 2000);
            }
        });

        // Social sign-in functions
        function signInWithGoogle() {
            alert('🔍 Google Sign-In integration coming soon!\n\nFor now, please use the email/password login.');
        }

        function signInWithLinkedIn() {
            alert('💼 LinkedIn Sign-In integration coming soon!\n\nFor now, please use the email/password login.');
        }

        // Forgot password function
        function showForgotPassword() {
            const email = prompt('Enter your email address to reset your password:');
            if (email && validateEmail(email)) {
                alert(`📧 Password reset link sent to ${email}\n\nPlease check your inbox and follow the instructions to reset your password.`);
            } else if (email) {
                alert('❌ Please enter a valid email address');
            }
        }

        // Add input event listeners for real-time validation
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            const isLoggedIn = localStorage.getItem('cubeai_user_logged_in');
            const userEmail = localStorage.getItem('cubeai_user_email');

            if (isLoggedIn === 'true' && userEmail) {
                // User is already logged in, redirect to dashboard
                window.location.href = 'dashboard.html';
                return;
            }
            // Real-time email validation
            document.getElementById('signinEmail').addEventListener('input', function() {
                if (this.value && validateEmail(this.value)) {
                    clearFieldError('signinEmail', 'signinEmailError');
                }
            });
            
            document.getElementById('signupEmail').addEventListener('input', function() {
                if (this.value && validateEmail(this.value)) {
                    clearFieldError('signupEmail', 'signupEmailError');
                }
            });
            
            // Real-time password confirmation
            document.getElementById('confirmPassword').addEventListener('input', function() {
                const password = document.getElementById('signupPassword').value;
                if (this.value && this.value === password) {
                    clearFieldError('confirmPassword', 'confirmPasswordError');
                }
            });
            
            // Console log for demo
            console.log('🚀 Cube AI Solution - Candidate Portal');
            console.log('Demo Credentials:');
            console.log('Email: <EMAIL>');
            console.log('Password: password123');
        });
    </script>
</body>
</html>