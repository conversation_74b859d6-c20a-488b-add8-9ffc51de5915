# Cube AI Solutions - Private Recruiter Portal

![Cube AI Solutions](https://img.shields.io/badge/Cube%20AI%20Solutions-Recruiter%20Portal-blue?style=for-the-badge)

**AI-Powered Recruitment Management System**

[![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=flat&logo=html5&logoColor=white)](https://developer.mozilla.org/en-US/docs/Web/HTML)
[![CSS3](https://img.shields.io/badge/CSS3-1572B6?style=flat&logo=css3&logoColor=white)](https://developer.mozilla.org/en-US/docs/Web/CSS)
[![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=flat&logo=javascript&logoColor=black)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

---

## 📋 Table of Contents

- [Overview](#overview)
- [Key Features](#key-features)
- [Installation](#installation)
- [Usage](#usage)
- [Project Structure](#project-structure)
- [Configuration](#configuration)
- [API Integration](#api-integration-ready)
- [Contributing](#contributing)
- [License](#license)
- [Support](#support)

---

## 🚀 Overview

**Cube AI Solutions Private Recruiter Portal** is a comprehensive, AI-powered recruitment management system designed to automate and streamline the entire hiring process. Built with modern web technologies and inspired by industry-leading platforms, this portal provides recruiters with powerful tools to manage candidates, jobs, and interviews efficiently.

---

## ✨ Key Features

- **Automated Resume Parsing & Candidate Profile Generation**
- **Job Description Analysis**
- **AI-Powered Resume Matching & Scoring**
- **Candidate Ranking & Dashboard**
- **Automated Email Notifications**
- **AI-Powered Interview Management**
- **Advanced Analytics & Reporting**
- **Role-Based Configuration**
- **Responsive Design**

---

## 🛠️ Installation

### Prerequisites

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Node.js and npm (for local server, optional)

### Quick Setup

```bash
# Clone or download the repository
git clone https://github.com/cubeaisolutions/recruiter-portal.git
cd recruiter-portal

# Install dependencies (if using live-server)
npm install
```

### Launch Options

#### Option 1: VS Code Live Server

1. Open VS Code in the project directory
2. Install the "Live Server" extension
3. Right-click `index.html`
4. Select "Open with Live Server"

#### Option 2: Node.js Live Server

```bash
npx live-server --port=5000 --open=index.html
```

#### Option 3: Python HTTP Server

```bash
python -m http.server 5000
```

Then visit: [http://localhost:5000](http://localhost:5000)

---

## 📁 Project Structure

```text
cube-ai-recruiter-portal/
│
├── assets/                    # Static assets
│   ├── images/               # Image files
│   └── icons/                # Icon files
├── css/                      # Stylesheets
│   └── styles.css           # Main stylesheet
├── js/                       # JavaScript files
│   └── scripts.js           # Main application logic
├── pages/                    # HTML pages
│   ├── login.html           # Authentication page
│   └── dashboard.html       # Main dashboard
├── config/                   # Configuration files
│   └── app.config.js        # Application settings
├── node_modules/             # Dependencies
├── index.html               # Entry point
├── package.json             # Project configuration
├── package-lock.json        # Dependency lock file
└── README.md                # Documentation
```

---

## ⚙️ Configuration

- **Scoring Weights:** Easily configurable in `scripts.js`
- **Email Templates:** Customizable in the configuration section
- **Search & Filter:** Advanced options available

---

## 🔗 API Integration Ready

The portal is designed for easy integration with backend services. See the code comments for example endpoints and configuration.

---

## 🤝 Contributing

We welcome contributions! Please fork the repo, create a feature branch, and submit a pull request.

---

## 📄 License

**Proprietary License**  
© 2025 Cube AI Solutions. All rights reserved.  
For licensing inquiries: <EMAIL>

---

## 📞 Support

- **General:** <EMAIL>
- **Technical:** <EMAIL>
- **Documentation:** [docs.cubeaisolutions.com](https://docs.cubeaisolutions.com)

---

*Empowering organizations to find exceptional talent through artificial intelligence*

🌐 [cubeaisolutions.com](https://cubeaisolutions.com) | 📧 [<EMAIL>](mailto:<EMAIL>) | 🐦 [@CubeAISolutions](https://twitter.com/CubeAISolutions)