<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube AI Solution - Join Our Team</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎯</text></svg>">
</head>
<body>
    <!-- Authentication Check -->
    <script>
        // Check if user is logged in
        const isLoggedIn = localStorage.getItem('cubeai_user_logged_in');
        const userEmail = localStorage.getItem('cubeai_user_email');

        if (!isLoggedIn || isLoggedIn !== 'true' || !userEmail) {
            // User not logged in, redirect to login
            window.location.href = 'login.html';
        }
    </script>

    <div class="container">
        <header>
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">C³</div>
                    <div class="company-info">
                        <h1>Cube AI Solution</h1>
                        <p>Private Tech Limited Company | Innovating Tomorrow</p>
                    </div>
                </div>
                <div class="header-nav">
                    <a href="https://www.cubeaisolutions.com/About%20us/about%20us.html" class="nav-btn">About Us</a>
                    <a href="#" class="nav-btn">Culture</a>
                    <a href="#" class="nav-btn">Contact</a>
                    <button class="nav-btn logout-btn" onclick="logout()">Logout</button>
                </div>
            </div>
        </header>

        <div class="main-content">
            <aside class="sidebar">
                <div class="search-section">
                    <h3>🔍 Find Your Dream Job</h3>
                    <input type="text" class="search-input" placeholder="Search jobs, skills, or keywords..." id="searchInput">
                </div>

                <div class="filter-section">
                    <div class="filter-group">
                        <h4>Department</h4>
                        <div class="filter-option">
                            <input type="checkbox" id="ai" value="ai">
                            <label for="ai">Artificial Intelligence</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="iot" value="iot">
                            <label for="iot">IoT & Embedded Systems</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="web" value="web">
                            <label for="web">Web Development</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="marketing" value="marketing">
                            <label for="marketing">Digital Marketing</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="data" value="data">
                            <label for="data">Data Science</label>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h4>Experience Level</h4>
                        <div class="filter-option">
                            <input type="checkbox" id="entry" value="entry">
                            <label for="entry">Entry Level (0-2 years)</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="mid" value="mid">
                            <label for="mid">Mid Level (3-5 years)</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="senior" value="senior">
                            <label for="senior">Senior Level (5+ years)</label>
                        </div>
                    </div>

                    <div class="filter-group">
                        <h4>Work Type</h4>
                        <div class="filter-option">
                            <input type="checkbox" id="fulltime" value="fulltime">
                            <label for="fulltime">Full-time</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="parttime" value="parttime">
                            <label for="parttime">Part-time</label>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="remote" value="remote">
                            <label for="remote">Remote</label>
                        </div>
                    </div>
                </div>

                <div class="cv-upload-section">
                    <h3>🚀 AI-Powered CV Analysis</h3>
                    <p>Upload your CV and get instant AI-powered matching scores for all positions!</p>
                    <div class="upload-area" onclick="document.getElementById('cvFile').click()">
                        <div>📄</div>
                        <p>Click to upload your CV</p>
                        <small>PDF, DOC, DOCX (Max 5MB)</small>
                    </div>
                    <input type="file" id="cvFile" accept=".pdf,.doc,.docx" style="display: none;" onchange="analyzeCv()">
                    <button class="upload-btn" onclick="document.getElementById('cvFile').click()">Choose File</button>
                </div>
            </aside>

            <main class="job-listings">
                <div class="listings-header">
                    <h2>Available Positions</h2>
                    <div class="job-count">Showing <span id="jobCount">12</span> jobs</div>
                </div>

                <div id="jobList">
                    <!-- AI/ML Jobs -->
                    <div class="job-card" data-category="ai" data-level="senior">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Senior AI/ML Engineer</div>
                                <div class="job-meta">
                                    <span>📍 Hybrid - Chennai</span>
                                    <span>💰 ₹15-25 LPA</span>
                                    <span>👥 3-6 years</span>
                                </div>
                            </div>
                            <div class="job-department">Artificial Intelligence</div>
                        </div>
                        <div class="job-description">
                            Lead the development of cutting-edge AI solutions including NLP, computer vision, and machine learning models. Work with LLMs, deep learning frameworks, and deploy AI solutions at scale.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">TensorFlow</span>
                            <span class="skill-tag">PyTorch</span>
                            <span class="skill-tag">NLP</span>
                            <span class="skill-tag">Computer Vision</span>
                            <span class="skill-tag">LLMs</span>
                            <span class="skill-tag">AWS/Azure</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Senior AI/ML Engineer')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 2 days ago</div>
                        </div>
                    </div>

                    <div class="job-card" data-category="ai" data-level="mid">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Machine Learning Engineer</div>
                                <div class="job-meta">
                                    <span>📍 Remote</span>
                                    <span>💰 ₹12-18 LPA</span>
                                    <span>👥 2-4 years</span>
                                </div>
                            </div>
                            <div class="job-department">Artificial Intelligence</div>
                        </div>
                        <div class="job-description">
                            Build and deploy machine learning models for production environments. Work on recommendation systems, predictive analytics, and automated ML pipelines.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">Scikit-learn</span>
                            <span class="skill-tag">MLOps</span>
                            <span class="skill-tag">Docker</span>
                            <span class="skill-tag">Kubernetes</span>
                            <span class="skill-tag">SQL</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Machine Learning Engineer')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 1 week ago</div>
                        </div>
                    </div>

                    <!-- IoT Jobs -->
                    <div class="job-card" data-category="iot" data-level="senior">
                        <div class="job-header">
                            <div>
                                <div class="job-title">IoT Solutions Architect</div>
                                <div class="job-meta">
                                    <span>📍 Bangalore</span>
                                    <span>💰 ₹18-28 LPA</span>
                                    <span>👥 5-8 years</span>
                                </div>
                            </div>
                            <div class="job-department">IoT & Embedded</div>
                        </div>
                        <div class="job-description">
                            Design and implement end-to-end IoT solutions including device connectivity, edge computing, cloud integration, and real-time analytics for smart city and industrial IoT projects.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">IoT Protocols</span>
                            <span class="skill-tag">MQTT</span>
                            <span class="skill-tag">Edge Computing</span>
                            <span class="skill-tag">Azure IoT</span>
                            <span class="skill-tag">C/C++</span>
                            <span class="skill-tag">Embedded Systems</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('IoT Solutions Architect')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 3 days ago</div>
                        </div>
                    </div>

                    <div class="job-card" data-category="iot" data-level="mid">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Embedded Systems Developer</div>
                                <div class="job-meta">
                                    <span>📍 Pune</span>
                                    <span>💰 ₹10-16 LPA</span>
                                    <span>👥 2-5 years</span>
                                </div>
                            </div>
                            <div class="job-department">IoT & Embedded</div>
                        </div>
                        <div class="job-description">
                            Develop firmware and software for IoT devices, sensors, and embedded systems. Work on microcontroller programming, sensor integration, and wireless communication protocols.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">C/C++</span>
                            <span class="skill-tag">Arduino</span>
                            <span class="skill-tag">Raspberry Pi</span>
                            <span class="skill-tag">RTOS</span>
                            <span class="skill-tag">PCB Design</span>
                            <span class="skill-tag">Bluetooth/WiFi</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Embedded Systems Developer')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 5 days ago</div>
                        </div>
                    </div>

                    <!-- Web Development Jobs -->
                    <div class="job-card" data-category="web" data-level="senior">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Full Stack Developer (React/Node.js)</div>
                                <div class="job-meta">
                                    <span>📍 Hybrid - Mumbai</span>
                                    <span>💰 ₹14-22 LPA</span>
                                    <span>👥 4-7 years</span>
                                </div>
                            </div>
                            <div class="job-department">Web Development</div>
                        </div>
                        <div class="job-description">
                            Build scalable web applications using modern JavaScript frameworks. Lead frontend and backend development for AI-powered SaaS products and enterprise solutions.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">React</span>
                            <span class="skill-tag">Node.js</span>
                            <span class="skill-tag">TypeScript</span>
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">GraphQL</span>
                            <span class="skill-tag">AWS</span>
                            <span class="skill-tag">Docker</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Full Stack Developer')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 1 day ago</div>
                        </div>
                    </div>

                    <div class="job-card" data-category="web" data-level="mid">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Frontend Developer (React/Vue)</div>
                                <div class="job-meta">
                                    <span>📍 Remote</span>
                                    <span>💰 ₹8-14 LPA</span>
                                    <span>👥 2-4 years</span>
                                </div>
                            </div>
                            <div class="job-department">Web Development</div>
                        </div>
                        <div class="job-description">
                            Create responsive, user-friendly interfaces for AI and IoT applications. Work with design systems, implement complex UI components, and optimize performance.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">React</span>
                            <span class="skill-tag">Vue.js</span>
                            <span class="skill-tag">JavaScript</span>
                            <span class="skill-tag">CSS3</span>
                            <span class="skill-tag">Tailwind</span>
                            <span class="skill-tag">REST APIs</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Frontend Developer')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 4 days ago</div>
                        </div>
                    </div>

                    <!-- Digital Marketing Jobs -->
                    <div class="job-card" data-category="marketing" data-level="senior">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Digital Marketing Manager</div>
                                <div class="job-meta">
                                    <span>📍 Delhi NCR</span>
                                    <span>💰 ₹12-18 LPA</span>
                                    <span>👥 4-6 years</span>
                                </div>
                            </div>
                            <div class="job-department">Digital Marketing</div>
                        </div>
                        <div class="job-description">
                            Lead digital marketing strategies for AI and tech products. Manage multi-channel campaigns, growth hacking, and data-driven marketing initiatives for B2B tech solutions.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">Google Ads</span>
                            <span class="skill-tag">Facebook Ads</span>
                            <span class="skill-tag">SEO/SEM</span>
                            <span class="skill-tag">Analytics</span>
                            <span class="skill-tag">Content Strategy</span>
                            <span class="skill-tag">HubSpot</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Digital Marketing Manager')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 2 days ago</div>
                        </div>
                    </div>

                    <div class="job-card" data-category="marketing" data-level="mid">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Growth Marketing Specialist</div>
                                <div class="job-meta">
                                    <span>📍 Hybrid - Hyderabad</span>
                                    <span>💰 ₹8-12 LPA</span>
                                    <span>👥 2-4 years</span>
                                </div>
                            </div>
                            <div class="job-department">Digital Marketing</div>
                        </div>
                        <div class="job-description">
                            Drive user acquisition and retention for AI-powered products. Implement growth experiments, optimize conversion funnels, and scale marketing campaigns.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">Growth Hacking</span>
                            <span class="skill-tag">A/B Testing</span>
                            <span class="skill-tag">Mixpanel</span>
                            <span class="skill-tag">Email Marketing</span>
                            <span class="skill-tag">Social Media</span>
                            <span class="skill-tag">CRO</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Growth Marketing Specialist')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 6 days ago</div>
                        </div>
                    </div>

                    <!-- Data Science Jobs -->
                    <div class="job-card" data-category="data" data-level="senior">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Data Scientist - AI Analytics</div>
                                <div class="job-meta">
                                    <span>📍 Bangalore</span>
                                    <span>💰 ₹16-24 LPA</span>
                                    <span>👥 4-7 years</span>
                                </div>
                            </div>
                            <div class="job-department">Data Science</div>
                        </div>
                        <div class="job-description">
                            Extract insights from large datasets to drive AI product decisions. Build predictive models, conduct statistical analysis, and create data-driven solutions for business problems.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">R</span>
                            <span class="skill-tag">SQL</span>
                            <span class="skill-tag">Tableau</span>
                            <span class="skill-tag">Statistics</span>
                            <span class="skill-tag">Big Data</span>
                            <span class="skill-tag">Spark</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Data Scientist')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 3 days ago</div>
                        </div>
                    </div>

                    <div class="job-card" data-category="data" data-level="entry">
                        <div class="job-header">
                            <div>
                                <div class="job-title">Junior Data Analyst</div>
                                <div class="job-meta">
                                    <span>📍 Remote</span>
                                    <span>💰 ₹6-10 LPA</span>
                                    <span>👥 1-3 years</span>
                                </div>
                            </div>
                            <div class="job-department">Data Science</div>
                        </div>
                        <div class="job-description">
                            Support data analysis and reporting for AI projects. Create dashboards, perform data cleaning, and assist in building analytics solutions for business intelligence.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">SQL</span>
                            <span class="skill-tag">Excel</span>
                            <span class="skill-tag">Power BI</span>
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">Statistics</span>
                            <span class="skill-tag">Data Visualization</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('Junior Data Analyst')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 1 week ago</div>
                        </div>
                    </div>

                    <!-- More AI Jobs -->
                    <div class="job-card" data-category="ai" data-level="entry">
                        <div class="job-header">
                            <div>
                                <div class="job-title">AI Research Intern</div>
                                <div class="job-meta">
                                    <span>📍 Hybrid - Chennai</span>
                                    <span>💰 ₹4-6 LPA</span>
                                    <span>👥 0-2 years</span>
                                </div>
                            </div>
                            <div class="job-department">Artificial Intelligence</div>
                        </div>
                        <div class="job-description">
                            Join our research team to work on cutting-edge AI projects. Assist in developing new algorithms, conducting experiments, and contributing to research publications.
                        </div>
                        <div class="job-skills">
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">Mathematics</span>
                            <span class="skill-tag">Research</span>
                            <span class="skill-tag">TensorFlow</span>
                            <span class="skill-tag">Academic Writing</span>
                            <span class="skill-tag">Git</span>
                        </div>
                        <div class="job-actions">
                            <button class="apply-btn" onclick="openApplication('AI Research Intern')">Apply Now</button>
                            <button class="save-btn">Save Job</button>
                            <div class="job-posted">Posted 5 days ago</div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Application Modal -->
    <div id="applicationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeApplication()">&times;</span>
            <h2>Apply for <span id="modalJobTitle"></span></h2>
            <form class="application-form" onsubmit="submitApplication(event)">
                <div class="form-group">
                    <label for="fullName">Full Name *</label>
                    <input type="text" id="fullName" name="fullName" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone Number *</label>
                    <input type="tel" id="phone" name="phone" required>
                </div>
                
                <div class="form-group">
                    <label for="experience">Years of Experience</label>
                    <select id="experience" name="experience">
                        <option value="">Select Experience</option>
                        <option value="0-1">0-1 years</option>
                        <option value="1-3">1-3 years</option>
                        <option value="3-5">3-5 years</option>
                        <option value="5-8">5-8 years</option>
                        <option value="8+">8+ years</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="currentSalary">Current Salary (LPA)</label>
                    <input type="number" id="currentSalary" name="currentSalary" placeholder="e.g., 12">
                </div>
                
                <div class="form-group">
                    <label for="coverLetter">Cover Letter</label>
                    <textarea id="coverLetter" name="coverLetter" rows="4" placeholder="Tell us why you're perfect for this role..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="applicationCv">Upload CV/Resume *</label>
                    <input type="file" id="applicationCv" name="applicationCv" accept=".pdf,.doc,.docx" required onchange="analyzeApplicationCv()">
                </div>
                
                <div id="cvAnalysisResult" class="cv-analysis" style="display: none;">
                    <h4>🔍 AI CV Analysis Results</h4>
                    <div class="analysis-score">
                        <div class="score-item">
                            <div class="score-value" id="skillsMatch">-</div>
                            <div class="score-label">Skills Match</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="skillsProgress"></div>
                            </div>
                        </div>
                        <div class="score-item">
                            <div class="score-value" id="experienceMatch">-</div>
                            <div class="score-label">Experience</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="experienceProgress"></div>
                            </div>
                        </div>
                        <div class="score-item">
                            <div class="score-value" id="overallMatch">-</div>
                            <div class="score-label">Overall Fit</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="overallProgress"></div>
                            </div>
                        </div>
                    </div>
                    <div id="analysisInsights"></div>
                </div>
                
                <button type="submit" class="submit-btn">Submit Application 🚀</button>
            </form>
        </div>
    </div>

    <script src="../js/scripts.js"></script>

    <!-- Authentication and User Management Script -->
    <script>
        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                // Clear authentication data
                localStorage.removeItem('cubeai_user_logged_in');
                localStorage.removeItem('cubeai_user_email');
                localStorage.removeItem('cubeai_user_name');

                // Redirect to login page
                window.location.href = 'login.html';
            }
        }
    </script>
</body>
</html>