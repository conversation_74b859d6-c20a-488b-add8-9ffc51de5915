<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube AI Solution - Candidate Portal</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👤</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            height: 70px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .company-info h1 {
            font-size: 1.6rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
            letter-spacing: -0.5px;
        }

        .company-info p {
            font-size: 0.85rem;
            color: #718096;
            margin: 0;
            font-weight: 400;
        }

        .header-nav {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }

        .nav-link {
            color: #4a5568;
            text-decoration: none;
            font-size: 0.95rem;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .nav-link.active {
            color: #667eea;
            font-weight: 600;
            background: rgba(102, 126, 234, 0.15);
        }

        /* Main Container */
        .main-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 70px);
            padding: 2rem;
            position: relative;
        }

        .main-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        /* Login Card */
        .login-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            padding: 0;
            width: 100%;
            max-width: 450px;
            position: relative;
            z-index: 10;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .login-container {
            padding: 3rem 2.5rem;
            width: 100%;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
        }

        .login-subtitle {
            font-size: 1rem;
            color: #718096;
            font-weight: 400;
            line-height: 1.5;
        }

        /* Tab Switcher */
        .tab-switcher {
            display: flex;
            background: #f7fafc;
            border-radius: 10px;
            padding: 6px;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
        }

        .tab-btn {
            flex: 1;
            padding: 0.875rem 1.5rem;
            background: transparent;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 600;
            color: #718096;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }

        /* Form Styles */
        .auth-form {
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: #2d3748;
            font-size: 0.95rem;
            font-weight: 600;
            letter-spacing: 0.025em;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f7fafc;
            color: #2d3748;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .form-input.error {
            border-color: #d93025;
        }

        .error-message {
            color: #d93025;
            font-size: 0.8rem;
            margin-top: 0.5rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        /* Form Options */
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #1a73e8;
        }

        .checkbox-group label {
            color: #3c4043;
            font-weight: 400;
            cursor: pointer;
            margin: 0;
        }

        .forgot-link {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        /* Buttons */
        .btn-primary {
            width: 100%;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.025em;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-primary.loading {
            pointer-events: none;
        }

        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
        }

        .btn-primary.loading .btn-text {
            opacity: 0;
        }

        .btn-primary.loading .loading-spinner {
            opacity: 1;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Alternative Actions */
        .alt-actions {
            text-align: center;
            padding-top: 1.5rem;
            border-top: 1px solid #e8eaed;
        }

        .alt-actions p {
            color: #5f6368;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .btn-secondary {
            width: 100%;
            padding: 0.875rem 1.5rem;
            background: white;
            color: #3c4043;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .btn-secondary:hover {
            background: #f8f9fa;
            border-color: #c4c7c5;
        }

        .signup-link {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link:hover {
            text-decoration: underline;
        }

        /* Footer */
        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            text-align: center;
            margin-top: 2rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .footer-links a:hover {
            color: white;
            text-decoration: underline;
        }

        .footer-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.85rem;
            font-weight: 400;
        }

        /* Success Message */
        .success-message {
            background: #e8f5e8;
            color: #137333;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #137333;
            display: none;
        }

        .success-message.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .login-card {
                max-width: 100%;
                margin: 0;
            }

            .login-container {
                padding: 2rem 1.5rem;
            }

            .header-content {
                padding: 0 1rem;
            }

            .company-info h1 {
                font-size: 1.4rem;
            }

            .login-title {
                font-size: 1.75rem;
            }

            .footer-links {
                flex-wrap: wrap;
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .header-content {
                flex-direction: column;
                height: auto;
                padding: 1rem;
                gap: 1rem;
            }

            .header-nav {
                gap: 1rem;
            }

            .info-content h2 {
                font-size: 1.5rem;
            }

            .login-container {
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="company-logo">C³</div>
                <div class="company-info">
                    <h1>Cube AI Solution</h1>
                    <p>Candidate Portal</p>
                </div>
            </div>
            <nav class="header-nav">
                <a href="https://www.cubeaisolutions.com/About%20us/about%20us.html" class="nav-link">About Us</a>
                <a href="https://www.cubeaisolutions.com/Service%20Page/Service.html" class="nav-link">Careers</a>
                <a href="#" class="nav-link active">Login</a>
                <a href="https://www.cubeaisolutions.com/contact-us/contact.html" class="nav-link">Contact</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <div class="login-card">
            <div class="login-container">
                <div class="login-header">
                    <h1 class="login-title">Welcome Back</h1>
                    <p class="login-subtitle">Sign in to access your candidate dashboard</p>
                </div>

                <!-- Tab Switcher -->
                <div class="tab-switcher">
                    <button class="tab-btn active" onclick="switchTab('signin')">Sign In</button>
                    <button class="tab-btn" onclick="switchTab('signup')">Sign Up</button>
                </div>

                <!-- Success Message -->
                <div id="successMessage" class="success-message">
                    <strong>Account created successfully!</strong> Please sign in with your credentials.
                </div>

                <!-- Sign In Form -->
                <form id="signinForm" class="auth-form">
                    <div class="form-group">
                        <label for="signinEmail">Email Address</label>
                        <input type="email" id="signinEmail" class="form-input" placeholder="Enter your email address" required>
                        <div class="error-message" id="signinEmailError">Please enter a valid email address</div>
                    </div>

                    <div class="form-group">
                        <label for="signinPassword">Password</label>
                        <input type="password" id="signinPassword" class="form-input" placeholder="Enter your password" required>
                        <div class="error-message" id="signinPasswordError">Password is required</div>
                    </div>

                    <div class="form-options">
                        <div class="checkbox-group">
                            <input type="checkbox" id="rememberMe">
                            <label for="rememberMe">Remember me</label>
                        </div>
                        <a href="#" class="forgot-link" onclick="showForgotPassword()">Forgot password?</a>
                    </div>

                    <button type="submit" class="btn-primary">
                        <span class="btn-text">Sign In</span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>

                <!-- Sign Up Form -->
                <form id="signupForm" class="auth-form" style="display: none;">
                    <div class="form-group">
                        <label for="signupName">Full Name</label>
                        <input type="text" id="signupName" class="form-input" placeholder="Enter your full name" required>
                        <div class="error-message" id="signupNameError">Full name is required</div>
                    </div>

                    <div class="form-group">
                        <label for="signupEmail">Email Address</label>
                        <input type="email" id="signupEmail" class="form-input" placeholder="Enter your email address" required>
                        <div class="error-message" id="signupEmailError">Please enter a valid email address</div>
                    </div>

                    <div class="form-group">
                        <label for="signupPhone">Phone Number</label>
                        <input type="tel" id="signupPhone" class="form-input" placeholder="Enter your phone number" required>
                        <div class="error-message" id="signupPhoneError">Phone number is required</div>
                    </div>

                    <div class="form-group">
                        <label for="signupPassword">Password</label>
                        <input type="password" id="signupPassword" class="form-input" placeholder="Create a password" required>
                        <div class="error-message" id="signupPasswordError">Password must be at least 6 characters</div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" class="form-input" placeholder="Confirm your password" required>
                        <div class="error-message" id="confirmPasswordError">Passwords do not match</div>
                    </div>

                    <div class="form-options">
                        <div class="checkbox-group">
                            <input type="checkbox" id="agreeTerms" required>
                            <label for="agreeTerms">I agree to the <a href="#" class="signup-link">Terms of Service</a></label>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary">
                        <span class="btn-text">Create Account</span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>

                <!-- Alternative Actions -->
                <div class="alt-actions">
                    <p>Or continue with</p>
                    <button class="btn-secondary" onclick="signInWithGoogle()">
                        <span style="margin-right: 8px;">🔍</span> Sign in with Google
                    </button>
                    <button class="btn-secondary" onclick="signInWithLinkedIn()">
                        <span style="margin-right: 8px;">💼</span> Sign in with LinkedIn
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Help Center</a>
                <a href="#">Contact Support</a>
            </div>
            <p class="footer-text">© 2025 Cube AI Solution. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Global variables
        let currentTab = 'signin';

        // Simple User Database using localStorage
        class UserDatabase {
            constructor() {
                this.storageKey = 'cubeai_users_db';
            }

            // Get all users from localStorage
            getAllUsers() {
                const users = localStorage.getItem(this.storageKey);
                return users ? JSON.parse(users) : [];
            }

            // Save users to localStorage
            saveUsers(users) {
                localStorage.setItem(this.storageKey, JSON.stringify(users));
            }

            // Register a new user
            registerUser(userData) {
                const users = this.getAllUsers();

                // Check if email already exists
                const existingUser = users.find(user => user.email === userData.email);
                if (existingUser) {
                    return { success: false, message: 'Email already registered' };
                }

                // Add new user
                const newUser = {
                    id: Date.now().toString(),
                    name: userData.name,
                    email: userData.email,
                    phone: userData.phone,
                    password: userData.password, // In real app, this should be hashed
                    createdAt: new Date().toISOString()
                };

                users.push(newUser);
                this.saveUsers(users);

                return { success: true, user: newUser };
            }

            // Authenticate user login
            authenticateUser(email, password) {
                const users = this.getAllUsers();
                const user = users.find(user => user.email === email && user.password === password);

                if (user) {
                    return { success: true, user: user };
                } else {
                    return { success: false, message: 'Invalid email or password' };
                }
            }
        }

        // Initialize user database
        const userDB = new UserDatabase();

        // Create default users if not exist
        function createDefaultUsers() {
            const users = userDB.getAllUsers();

            // Default users to create
            const defaultUsers = [
                {
                    id: 'default_001',
                    name: 'Cube AI User',
                    email: '<EMAIL>',
                    phone: '+1234567890',
                    password: 'user001',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'default_002',
                    name: 'Demo Candidate',
                    email: '<EMAIL>',
                    phone: '+1987654321',
                    password: 'password123',
                    createdAt: new Date().toISOString()
                }
            ];

            // Check and create each default user if not exists
            defaultUsers.forEach(defaultUser => {
                const existingUser = users.find(user => user.email === defaultUser.email);
                if (!existingUser) {
                    users.push(defaultUser);
                    console.log(`✅ Default user created: ${defaultUser.email} / ${defaultUser.password}`);
                }
            });

            // Save updated users list
            userDB.saveUsers(users);
        }

        // Initialize default users on page load
        createDefaultUsers();

        // Admin functions (accessible via browser console)
        window.cubeaiAdmin = {
            viewAllUsers: function() {
                const users = userDB.getAllUsers();
                console.table(users.map(user => ({
                    ID: user.id,
                    Name: user.name,
                    Email: user.email,
                    Phone: user.phone,
                    'Created At': new Date(user.createdAt).toLocaleString()
                })));
                return users;
            },

            clearAllUsers: function() {
                if (confirm('⚠️ This will delete all registered users. Are you sure?')) {
                    localStorage.removeItem('cubeai_users_db');
                    console.log('✅ All users cleared');
                }
            },

            getUserCount: function() {
                const count = userDB.getAllUsers().length;
                console.log(`👥 Total registered users: ${count}`);
                return count;
            }
        };

        // Tab switching functionality
        function switchTab(tab) {
            currentTab = tab;
            const signinForm = document.getElementById('signinForm');
            const signupForm = document.getElementById('signupForm');
            const tabBtns = document.querySelectorAll('.tab-btn');
            const successMessage = document.getElementById('successMessage');
            
            // Hide success message when switching tabs
            successMessage.classList.remove('show');
            
            // Update tab buttons
            tabBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide forms
            if (tab === 'signin') {
                signinForm.style.display = 'block';
                signupForm.style.display = 'none';
                document.querySelector('.login-title').textContent = 'Welcome Back';
                document.querySelector('.login-subtitle').textContent = 'Sign in to access your candidate dashboard';
            } else {
                signinForm.style.display = 'none';
                signupForm.style.display = 'block';
                document.querySelector('.login-title').textContent = 'Create Account';
                document.querySelector('.login-subtitle').textContent = 'Join our talent community today';
            }
            
            // Clear any error states
            clearFormErrors();
        }

        // Form validation functions
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function validatePhone(phone) {
            const phoneRegex = /^[\+]?[1-9][\d]{9,14}$/;
            return phoneRegex.test(phone.replace(/\s/g, ''));
        }

        function showFieldError(fieldId, errorId, message) {
            const field = document.getElementById(fieldId);
            const error = document.getElementById(errorId);
            
            field.classList.add('error');
            error.textContent = message;
            error.classList.add('show');
        }

        function clearFieldError(fieldId, errorId) {
            const field = document.getElementById(fieldId);
            const error = document.getElementById(errorId);
            
            field.classList.remove('error');
            error.classList.remove('show');
        }

        function clearFormErrors() {
            const errorMessages = document.querySelectorAll('.error-message');
            const errorFields = document.querySelectorAll('.form-input.error');
            
            errorMessages.forEach(error => error.classList.remove('show'));
            errorFields.forEach(field => field.classList.remove('error'));
        }

        // Sign In Form Handler
        document.getElementById('signinForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('signinEmail').value.trim();
            const password = document.getElementById('signinPassword').value;
            let isValid = true;
            
            // Clear previous errors
            clearFormErrors();
            
            // Validate email
            if (!email) {
                showFieldError('signinEmail', 'signinEmailError', 'Email address is required');
                isValid = false;
            } else if (!validateEmail(email)) {
                showFieldError('signinEmail', 'signinEmailError', 'Please enter a valid email address');
                isValid = false;
            }
            
            // Validate password
            if (!password) {
                showFieldError('signinPassword', 'signinPasswordError', 'Password is required');
                isValid = false;
            }
            
            if (isValid) {
                // Show loading state
                const submitBtn = this.querySelector('.btn-primary');
                submitBtn.classList.add('loading');
                
                // Authenticate with real user database
                setTimeout(() => {
                    // Use the user database to authenticate
                    const authResult = userDB.authenticateUser(email, password);

                    if (authResult.success) {
                        // Store login state in localStorage
                        localStorage.setItem('cubeai_user_logged_in', 'true');
                        localStorage.setItem('cubeai_user_email', authResult.user.email);
                        localStorage.setItem('cubeai_user_name', authResult.user.name);
                        localStorage.setItem('cubeai_user_id', authResult.user.id);

                        // Redirect to dashboard.html after successful login
                        window.location.href = "dashboard.html";
                    } else {
                        alert('❌ ' + authResult.message + '\n\nPlease check your email and password, or create a new account.');
                    }

                    submitBtn.classList.remove('loading');
                }, 1500);
            }
        });

        // Sign Up Form Handler
        document.getElementById('signupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('signupName').value.trim();
            const email = document.getElementById('signupEmail').value.trim();
            const phone = document.getElementById('signupPhone').value.trim();
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;
            let isValid = true;
            
            // Clear previous errors
            clearFormErrors();
            
            // Validate name
            if (!name) {
                showFieldError('signupName', 'signupNameError', 'Full name is required');
                isValid = false;
            }
            
            // Validate email
            if (!email) {
                showFieldError('signupEmail', 'signupEmailError', 'Email address is required');
                isValid = false;
            } else if (!validateEmail(email)) {
                showFieldError('signupEmail', 'signupEmailError', 'Please enter a valid email address');
                isValid = false;
            }
            
            // Validate phone
            if (!phone) {
                showFieldError('signupPhone', 'signupPhoneError', 'Phone number is required');
                isValid = false;
            } else if (!validatePhone(phone)) {
                showFieldError('signupPhone', 'signupPhoneError', 'Please enter a valid phone number');
                isValid = false;
            }
            
            // Validate password
            if (!password) {
                showFieldError('signupPassword', 'signupPasswordError', 'Password is required');
                isValid = false;
            } else if (password.length < 6) {
                showFieldError('signupPassword', 'signupPasswordError', 'Password must be at least 6 characters');
                isValid = false;
            }
            
            // Validate confirm password
            if (!confirmPassword) {
                showFieldError('confirmPassword', 'confirmPasswordError', 'Please confirm your password');
                isValid = false;
            } else if (password !== confirmPassword) {
                showFieldError('confirmPassword', 'confirmPasswordError', 'Passwords do not match');
                isValid = false;
            }
            
            // Validate terms agreement
            if (!agreeTerms) {
                alert('❌ Please agree to the Terms of Service to continue');
                isValid = false;
            }
            
            if (isValid) {
                // Show loading state
                const submitBtn = this.querySelector('.btn-primary');
                submitBtn.classList.add('loading');
                
                // Create account with real user database
                setTimeout(() => {
                    submitBtn.classList.remove('loading');

                    // Register user in database
                    const registrationResult = userDB.registerUser({
                        name: name,
                        email: email,
                        phone: phone,
                        password: password
                    });

                    if (registrationResult.success) {
                        // Store login state in localStorage (auto-login after signup)
                        localStorage.setItem('cubeai_user_logged_in', 'true');
                        localStorage.setItem('cubeai_user_email', registrationResult.user.email);
                        localStorage.setItem('cubeai_user_name', registrationResult.user.name);
                        localStorage.setItem('cubeai_user_id', registrationResult.user.id);

                        // Show success message briefly then redirect
                        document.getElementById('successMessage').innerHTML = '<strong>Account created successfully!</strong> Welcome to Cube AI Solutions!';
                        document.getElementById('successMessage').classList.add('show');

                        setTimeout(() => {
                            // Redirect to dashboard after successful signup
                            window.location.href = "dashboard.html";
                        }, 2000);
                    } else {
                        // Show error message
                        alert('❌ Registration failed: ' + registrationResult.message);
                    }
                }, 1500);
            }
        });

        // Social sign-in functions
        function signInWithGoogle() {
            alert('🔍 Google Sign-In integration coming soon!\n\nFor now, please use the email/password login.');
        }

        function signInWithLinkedIn() {
            alert('💼 LinkedIn Sign-In integration coming soon!\n\nFor now, please use the email/password login.');
        }

        // Forgot password function
        function showForgotPassword() {
            const email = prompt('Enter your email address to reset your password:');
            if (email && validateEmail(email)) {
                alert(`📧 Password reset link sent to ${email}\n\nPlease check your inbox and follow the instructions to reset your password.`);
            } else if (email) {
                alert('❌ Please enter a valid email address');
            }
        }

        // Add input event listeners for real-time validation
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            const isLoggedIn = localStorage.getItem('cubeai_user_logged_in');
            const userEmail = localStorage.getItem('cubeai_user_email');

            if (isLoggedIn === 'true' && userEmail) {
                // User is already logged in, redirect to dashboard
                window.location.href = 'dashboard.html';
                return;
            }
            // Real-time email validation
            document.getElementById('signinEmail').addEventListener('input', function() {
                if (this.value && validateEmail(this.value)) {
                    clearFieldError('signinEmail', 'signinEmailError');
                }
            });
            
            document.getElementById('signupEmail').addEventListener('input', function() {
                if (this.value && validateEmail(this.value)) {
                    clearFieldError('signupEmail', 'signupEmailError');
                }
            });
            
            // Real-time password confirmation
            document.getElementById('confirmPassword').addEventListener('input', function() {
                const password = document.getElementById('signupPassword').value;
                if (this.value && this.value === password) {
                    clearFieldError('confirmPassword', 'confirmPasswordError');
                }
            });
            
            // Console log for system info
            console.log('🚀 Cube AI Solution - Candidate Portal');
            console.log('✅ Real Authentication System Active');
            console.log('📝 Default Login Credentials:');
            console.log('   Option 1 - Email: <EMAIL> | Password: user001');
            console.log('   Option 2 - Email: <EMAIL> | Password: password123');
            console.log('💾 User data stored securely in browser');
            console.log('🔧 Admin functions: cubeaiAdmin.viewAllUsers(), cubeaiAdmin.getUserCount()');
        });
    </script>
</body>
</html>